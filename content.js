// Основной скрипт для парсинга данных
class GoszakupParser {
    constructor() {
        this.data = {};
        this.currentSite = this.detectSite();
        this.selectedLotId = null; // Добавляем отслеживание выбранного лота
        this.init();
    }

    // Определяем на каком сайте находимся
    detectSite() {
        const hostname = window.location.hostname;
        if (hostname.includes('v3bl.goszakup.gov.kz')) {
            return 'goszakup';
        } else if (hostname.includes('zakup.sk.kz')) {
            return 'samruk';
        }
        return 'unknown';
    }

    init() {
        console.log(`Parser initialized for site: ${this.currentSite}`);
        this.setupEventListeners();
        
        // Не парсим данные автоматически при инициализации
        // Данные будут парситься только при клике на лот или при явном вызове
        console.log('🔧 Парсер готов к работе. Кликните на лот для парсинга данных.');
    }

    // Настройка слушателей событий
    setupEventListeners() {
        // Слушаем клики на кнопки выбора лота
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('btn-select-lot') || 
                e.target.closest('.btn-select-lot') ||
                e.target.classList.contains('lot-select') ||
                e.target.closest('[data-lot-id]')) {
                e.preventDefault();
                
                // Ищем lotId в разных местах
                let lotId = e.target.getAttribute('data-lot-id') || 
                           e.target.closest('[data-lot-id]')?.getAttribute('data-lot-id') ||
                           e.target.getAttribute('data-id') ||
                           e.target.closest('[data-id]')?.getAttribute('data-id');
                
                console.log('🎯 Лот выбран:', lotId);
                this.selectedLotId = lotId;
                
                // Очищаем предыдущие данные перед парсингом нового лота
                this.data = {
                    source: 'goszakup',
                    sourceUrl: window.location.href
                };
                
                // Ждем немного, чтобы данные загрузились
                setTimeout(() => {
                    console.log('🔄 Перепарсим данные для выбранного лота:', lotId);
                    this.parseLotDetails(lotId);
                    this.parseDates(lotId);
                    this.parseGoszakupFinancialFields(lotId);
                    
                    // Отправляем обновленные данные
                    this.sendDataToPopup();
                    this.sendDataToWebhook();
                }, 1000);
            }
        });
        
        // Также слушаем изменения в DOM для автоматического определения активного лота
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' || mutation.type === 'attributes') {
                    // Ищем активный/выбранный лот
                    const activeLot = document.querySelector('.lot-active, .selected-lot, [class*="active"][data-lot-id]');
                    if (activeLot) {
                        const lotId = activeLot.getAttribute('data-lot-id') || activeLot.getAttribute('data-id');
                        if (lotId && lotId !== this.selectedLotId) {
                            console.log('🔄 Автоматически обнаружен активный лот:', lotId);
                            this.selectedLotId = lotId;
                            
                            // Очищаем предыдущие данные перед парсингом нового лота
                            this.data = {
                                source: 'goszakup',
                                sourceUrl: window.location.href
                            };
                            
                            setTimeout(() => {
                                console.log('🔄 Перепарсим данные для автоматически обнаруженного лота:', lotId);
                                this.parseLotDetails(lotId);
                                this.parseDates(lotId);
                                this.parseGoszakupFinancialFields(lotId);
                                
                                // Отправляем обновленные данные
                                this.sendDataToPopup();
                                this.sendDataToWebhook();
                            }, 500);
                        }
                    }
                }
            });
        });
        
        // Начинаем наблюдение за изменениями в DOM
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['class', 'data-lot-id', 'data-id']
        });
    }

    // Парсинг основных данных
    parseData() {
        this.data = {};
        
        console.log(`Начинаем парсинг данных для сайта: ${this.currentSite}`);
        
        if (this.currentSite === 'goszakup') {
            this.parseGoszakupData();
        } else if (this.currentSite === 'samruk') {
            this.parseSamrukData();
        } else {
            console.log('Неподдерживаемый сайт');
            return;
        }
        
        // Отправляем данные в popup
        this.sendDataToPopup();
        
        // Автоматически отправляем данные на webhook
        this.sendDataToWebhook();
        
        console.log('Спарсенные данные:', this.data);
    }

    // Парсинг данных для goszakup.gov.kz
    parseGoszakupData() {
        // Сначала ищем номер лота на всей странице
        this.parseLotNumber(this.selectedLotId);
        
        // Парсим даты начала и окончания
        this.parseDates(this.selectedLotId);
        
        // Парсим данные лота сразу (не ждем клика)
        this.parseLotDetails();
        
        // Дополнительный парсинг финансовых полей для goszakup
        this.parseGoszakupFinancialFields();
        
        // Добавляем источник
        this.data.source = 'goszakup';
        this.data.sourceUrl = window.location.href;
    }

    // Парсинг данных для zakup.sk.kz (Самрук Казына)
    parseSamrukData() {
        console.log('Парсим данные Самрук Казына...');
        
        // Парсим номер объявления
        this.parseSamrukAnnouncementNumber();
        
        // Парсим заказчика
        this.parseSamrukCustomer();
        
        // Парсим метод закупки
        this.parseSamrukPurchaseMethod();
        
        // Парсим даты торгов
        this.parseSamrukTradingDates();
        
        // Парсим лоты
        this.parseSamrukLots();
        
        // Добавляем источник
        this.data.source = 'samruk';
        this.data.sourceUrl = window.location.href;
    }

    // Отдельная функция для поиска номера лота (улучшенная)
    parseLotNumber(selectedLotId = null) {
        console.log('🔍 Ищем полный номер лота для selectedLotId:', selectedLotId);
        
        // Паттерн для номера лота: 8 цифр, дефис, буквы и возможно цифры
        const lotNumberPattern = /(\d{8}-[А-Я]{2,5}\d*)/g;
        
        // 1. ПРИОРИТЕТНЫЙ ПОИСК: Если передан selectedLotId, ищем полный номер в таблице лотов
        if (selectedLotId) {
            console.log('🎯 Приоритетный поиск для выбранного лота:', selectedLotId);
            
            // Ищем строку таблицы с данным data-lot-id
            const lotRow = document.querySelector(`tr[data-lot-id="${selectedLotId}"]`);
            if (lotRow) {
                console.log('✅ Найдена строка таблицы для лота:', selectedLotId);
                
                // Ищем полный номер лота в этой строке
                const rowText = lotRow.textContent;
                const match = rowText.match(lotNumberPattern);
                if (match) {
                    this.data.lotNumber = match[0];
                    console.log('✅ Полный номер лота найден в строке таблицы:', match[0]);
                    return;
                }
                
                // Альтернативный поиск: ищем в первой ячейке (обычно там номер лота)
                const firstCell = lotRow.querySelector('td:first-child');
                if (firstCell) {
                    const cellText = firstCell.textContent.trim();
                    console.log('📝 Текст первой ячейки:', cellText);
                    
                    const cellMatch = cellText.match(lotNumberPattern);
                    if (cellMatch) {
                        this.data.lotNumber = cellMatch[0];
                        console.log('✅ Полный номер лота найден в первой ячейке:', cellMatch[0]);
                        return;
                    }
                    
                    // Может быть номер лота содержится в selectedLotId + суффикс из ячейки
                    if (cellText.includes(selectedLotId)) {
                        const fullNumber = cellText.match(/\d{8}-[А-Я]{2,5}\d*/);
                        if (fullNumber) {
                            this.data.lotNumber = fullNumber[0];
                            console.log('✅ Составлен полный номер лота:', fullNumber[0]);
                            return;
                        }
                    }
                }
                
                // Поиск во всех ячейках строки
                const allCells = lotRow.querySelectorAll('td');
                for (let cell of allCells) {
                    const cellText = cell.textContent.trim();
                    const match = cellText.match(lotNumberPattern);
                    if (match) {
                        this.data.lotNumber = match[0];
                        console.log('✅ Полный номер лота найден в ячейке таблицы:', match[0]);
                        return;
                    }
                }
            }
            
            // Поиск в контейнере лота (альтернативный подход)
            const lotContainer = document.querySelector(`[data-lot-id="${selectedLotId}"]`);
            if (lotContainer) {
                const containerText = lotContainer.textContent;
                const match = containerText.match(lotNumberPattern);
                if (match) {
                    this.data.lotNumber = match[0];
                    console.log('✅ Полный номер лота найден в контейнере:', match[0]);
                    return;
                }
            }
        }
        
        // 2. Поиск в URL
        const urlMatch = window.location.href.match(lotNumberPattern);
        if (urlMatch) {
            this.data.lotNumber = urlMatch[0];
            console.log('✅ Номер лота найден в URL:', urlMatch[0]);
            return;
        }
        
        // 3. Поиск в title страницы
        const titleMatch = document.title.match(lotNumberPattern);
        if (titleMatch) {
            this.data.lotNumber = titleMatch[0];
            console.log('✅ Номер лота найден в title:', titleMatch[0]);
            return;
        }
        
        // 4. Поиск в заголовках (h1-h6)
        const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
        for (let heading of headings) {
            const match = heading.textContent.match(lotNumberPattern);
            if (match) {
                this.data.lotNumber = match[0];
                console.log('✅ Номер лота найден в заголовке:', match[0]);
                return;
            }
        }
        
        // 5. Поиск в тексте всей страницы
        const pageText = document.body.textContent;
        const allMatches = pageText.match(lotNumberPattern);
        if (allMatches && allMatches.length > 0) {
            // Если у нас есть selectedLotId, пытаемся найти совпадение, которое содержит его
            if (selectedLotId) {
                for (let match of allMatches) {
                    if (match.startsWith(selectedLotId + '-')) {
                        this.data.lotNumber = match;
                        console.log('✅ Номер лота найден по selectedLotId в тексте:', match);
                        return;
                    }
                }
            }
            
            // Берем первое совпадение
            this.data.lotNumber = allMatches[0];
            console.log('✅ Номер лота найден в тексте страницы:', allMatches[0]);
            console.log('Найдено всего совпадений:', allMatches.length);
            return;
        }
        
        // 6. Если ничего не найдено, но есть selectedLotId, сохраняем его
        if (selectedLotId) {
            this.data.lotNumber = selectedLotId;
            console.log('⚠️ Сохранен только selectedLotId (без суффикса):', selectedLotId);
            return;
        }
        
        console.log('❌ Номер лота не найден');
    }

    // Парсинг дат (начала и окончания приёма заявок)
    // Парсинг дат для goszakup с поддержкой выбранного лота
    parseDates(selectedLotId = null) {
        if (!window.location.hostname.includes('goszakup.gov.kz')) {
            console.log('❌ Парсинг дат: не на сайте goszakup.gov.kz');
            return;
        }
        
        console.log('📅 ========== НАЧИНАЕМ ПАРСИНГ ДАТ ==========');
        console.log('📅 Начинаем парсинг дат для лота:', selectedLotId || 'не указан');
        console.log('📅 URL страницы:', window.location.href);
        
        let startDate = null;
        let endDate = null;
        
        // 1. Попытка найти даты в контексте выбранного лота
        if (selectedLotId) {
            const lotContainer = document.querySelector(`[data-lot-id="${selectedLotId}"]`) ||
                                document.querySelector(`#lot-${selectedLotId}`) ||
                                document.querySelector(`[data-id="${selectedLotId}"]`) ||
                                document.querySelector(`.lot-container[data-lot="${selectedLotId}"]`);
            
            if (lotContainer) {
                console.log('🎯 Найден контейнер лота:', lotContainer);
                const lotDates = this.extractDatesFromContainer(lotContainer);
                if (lotDates.start && lotDates.end) {
                    startDate = lotDates.start;
                    endDate = lotDates.end;
                    console.log('✅ Даты найдены в контейнере лота:', { startDate, endDate });
                }
            }
        }
        
        // 2. Поиск по специфическим полям для дат приема заявок (исключаем публикацию)
        if (!startDate || !endDate) {
            console.log('🔍 Поиск специфических полей дат приема заявок...');
            
            const dateFields = [
                'Срок начала приема заявок',
                'Срок окончания приема заявок',
                'Дата и время начала приема заявок',
                'Дата и время окончания приема заявок',
                'Начало приема заявок',
                'Окончание приема заявок',
                'Прием заявок с',
                'Прием заявок до',
                'Подача заявок с',
                'Подача заявок до'
            ];
            
            for (let field of dateFields) {
                if (startDate && endDate) break;
                
                const dateValue = this.findValueByLabel(field, selectedLotId);
                if (dateValue) {
                    const parsedDate = this.parseDate(dateValue);
                    if (parsedDate && this.isValidDateTime(parsedDate)) {
                        console.log(`📅 Найдено поле "${field}": ${parsedDate}`);
                        
                        if (field.includes('начал') || field.includes('с')) {
                            if (!startDate) startDate = parsedDate;
                        } else if (field.includes('оконч') || field.includes('до')) {
                            if (!endDate) endDate = parsedDate;
                        }
                    }
                }
            }
        }
        
        // 2.5. ПРЯМОЙ ПОИСК В ТАБЛИЦАХ по точным названиям полей (ВЫСОКИЙ ПРИОРИТЕТ)
        if (!startDate || !endDate) {
            console.log('🔍 Прямой поиск дат приема заявок в таблицах...');
            
            const tables = document.querySelectorAll('table');
            for (let table of tables) {
                const rows = table.querySelectorAll('tr');
                for (let row of rows) {
                    const cells = row.querySelectorAll('td, th');
                    if (cells.length >= 2) {
                        const fieldName = cells[0].textContent.trim();
                        const fieldValue = cells[1].textContent.trim();
                        
                        console.log(`🔍 Проверяем поле таблицы: "${fieldName}" = "${fieldValue}"`);
                        
                        // Точные совпадения для дат приема заявок
                        if (fieldName === 'Срок начала приема заявок' ||
                            fieldName === 'Дата и время начала приема заявок' ||
                            fieldName === 'Начало приема заявок') {
                            
                            if (this.isValidDateTime(fieldValue) && !startDate) {
                                startDate = this.parseDate(fieldValue);
                                console.log(`✅ Дата начала найдена в таблице: "${startDate}"`);
                            }
                        }
                        
                        if (fieldName === 'Срок окончания приема заявок' ||
                            fieldName === 'Дата и время окончания приема заявок' ||
                            fieldName === 'Окончание приема заявок') {
                            
                            if (this.isValidDateTime(fieldValue) && !endDate) {
                                endDate = this.parseDate(fieldValue);
                                console.log(`✅ Дата окончания найдена в таблице: "${endDate}"`);
                            }
                        }
                        
                        // Исключаем даты публикации
                        if (fieldName.includes('публикац') || 
                            fieldName.includes('размещен') ||
                            fieldName.includes('объявлен') ||
                            fieldName === 'Дата публикации' ||
                            fieldName === 'Дата размещения' ||
                            fieldName === 'Дата объявления') {
                            
                            console.log(`❌ Поле исключено как дата публикации: "${fieldName}"`);
                            continue;
                        }
                    }
                }
            }
        }
        
        // 3. Поиск в input полях (только для приема заявок)
        if (!startDate || !endDate) {
            console.log('🔍 Поиск в input полях...');
            
            let searchScope = selectedLotId ? 
                (document.querySelector(`[data-lot-id="${selectedLotId}"]`) || document) : 
                document;
            
            const dateInputs = searchScope.querySelectorAll('input[type="text"].form-control[readonly]');
            console.log('Найдено input полей с датами:', dateInputs.length);
            
            const validDates = [];
            dateInputs.forEach((input, index) => {
                const value = input.value.trim();
                console.log(`Input ${index}: "${value}"`);
                
                // Проверяем, что это дата приема заявок, а не публикации
                const inputContext = this.getInputContext(input);
                const isApplicationDate = this.isApplicationRelatedDate(inputContext);
                
                if (value && this.isValidDateTime(value) && isApplicationDate) {
                    validDates.push(value);
                }
            });
            
            if (validDates.length >= 2) {
                validDates.sort();
                if (!startDate) startDate = validDates[0];
                if (!endDate) endDate = validDates[1];
                console.log('✅ Даты из input полей:', { startDate, endDate });
            }
        }
        
        // 4. Поиск в таблицах с исключением дат публикации
        if (!startDate || !endDate) {
            console.log('🔍 Поиск в таблицах...');
            
            const excludeFields = [
                'дата публикации',
                'дата размещения',
                'опубликован',
                'размещен',
                'дата объявления'
            ];
            
            const tableDates = this.findDatesInTables(selectedLotId, excludeFields);
            if (tableDates.start && !startDate) startDate = tableDates.start;
            if (tableDates.end && !endDate) endDate = tableDates.end;
        }
        
        // 4.5. УПРОЩЕННЫЙ ПОИСК ВСЕХ ДАТ на странице (если предыдущие методы не сработали)
        if (!startDate || !endDate) {
            console.log('🔍 Упрощенный поиск всех дат на странице...');
            
            const allInputs = document.querySelectorAll('input[type="text"], input[readonly]');
            const foundDates = [];
            
            allInputs.forEach((input, index) => {
                const value = input.value.trim();
                if (value && this.isValidDateTime(value)) {
                    console.log(`📅 Найдена дата в input ${index}: "${value}"`);
                    
                    // Проверяем контекст input поля
                    const context = this.getInputContext(input);
                    console.log(`🔍 Контекст input ${index}:`, context);
                    
                    // Исключаем даты публикации
                    const publicationKeywords = [
                        'публикац', 'размещен', 'объявлен', 'опубликован',
                        'дата публикации', 'дата размещения', 'дата объявления'
                    ];
                    
                    let isPublicationDate = false;
                    for (let keyword of publicationKeywords) {
                        if (context.toLowerCase().includes(keyword.toLowerCase())) {
                            console.log(`❌ Input ${index} исключен как дата публикации по ключевому слову: ${keyword}`);
                            isPublicationDate = true;
                            break;
                        }
                    }
                    
                    if (!isPublicationDate) {
                        foundDates.push(value);
                        console.log(`✅ Input ${index} добавлен в список дат: "${value}"`);
                    }
                }
            });
            
            // Также ищем в любых элементах, содержащих даты в формате ДД.ММ.ГГГГ ЧЧ:ММ:СС
            const allElements = document.querySelectorAll('*');
            allElements.forEach((el, index) => {
                if (el.children.length === 0) { // только листовые элементы
                    const text = el.textContent.trim();
                    if (text.match(/^\d{2}\.\d{2}\.\d{4}\s+\d{2}:\d{2}:\d{2}$/)) {
                        // Проверяем контекст элемента на предмет исключения дат публикации
                        const elementContext = el.parentElement ? 
                            (el.parentElement.textContent || el.parentElement.outerHTML) : '';
                        
                        const publicationKeywords = [
                            'публикац', 'размещен', 'объявлен', 'опубликован',
                            'дата публикации', 'дата размещения', 'дата объявления'
                        ];
                        
                        let isPublicationDate = false;
                        for (let keyword of publicationKeywords) {
                            if (elementContext.toLowerCase().includes(keyword.toLowerCase())) {
                                console.log(`❌ Элемент ${index} исключен как дата публикации: "${text}"`);
                                isPublicationDate = true;
                                break;
                            }
                        }
                        
                        if (!isPublicationDate) {
                            console.log(`📅 Найдена дата в элементе ${index}: "${text}"`);
                            foundDates.push(text);
                        }
                    }
                }
            });
            
            if (foundDates.length > 0) {
                console.log(`📅 Всего найдено дат упрощенным поиском (исключая публикацию): ${foundDates.length}`, foundDates);
                
                // Сортируем даты
                foundDates.sort();
                
                if (!startDate && foundDates.length >= 1) {
                    startDate = foundDates[0];
                    console.log('✅ Дата начала из упрощенного поиска:', startDate);
                }
                if (!endDate && foundDates.length >= 2) {
                    endDate = foundDates[1];
                    console.log('✅ Дата окончания из упрощенного поиска:', endDate);
                }
            }
        }
        
        // 5. Финальная проверка и корректировка
        if (startDate && endDate) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            
            // Если даты одинаковые, ищем альтернативы
            if (start.getTime() === end.getTime()) {
                console.log('⚠️ Найденные даты одинаковые, ищем альтернативы');
                const alternateDates = this.findAlternateDatesForLot(selectedLotId);
                if (alternateDates.start && alternateDates.end && alternateDates.start !== alternateDates.end) {
                    startDate = alternateDates.start;
                    endDate = alternateDates.end;
                    console.log('✅ Найдены альтернативные даты:', { startDate, endDate });
                }
            }
            
            // Если начальная дата больше конечной, меняем местами
            if (new Date(startDate) > new Date(endDate)) {
                [startDate, endDate] = [endDate, startDate];
                console.log('🔄 Поменяли даты местами');
            }
        }
        
        // Сохраняем результаты
        if (startDate && endDate) {
            this.data.startDate = startDate;
            this.data.endDate = endDate;
            console.log('✅ Финальные даты приема заявок:', {
                start: this.data.startDate,
                end: this.data.endDate
            });
        } else {
            console.log('❌ Не удалось найти корректные даты приема заявок');
            console.log('❌ startDate:', startDate);
            console.log('❌ endDate:', endDate);
        }
        
        console.log('📅 ========== ЗАВЕРШЕНИЕ ПАРСИНГА ДАТ ==========');
        console.log('📅 Итоговое состояние this.data:', JSON.stringify(this.data, null, 2));
        console.log('📅 =============================================');
    }

    // === ФУНКЦИИ ПАРСИНГА ДЛЯ САМРУК КАЗЫНА ===

    // Парсинг номера объявления
    parseSamrukAnnouncementNumber() {
        // Ищем номер объявления в различных местах
        let numberElement = document.querySelector('.m-modal__num');
        
        if (!numberElement) {
            // Альтернативный поиск номера объявления
            const elements = document.querySelectorAll('div');
            for (let element of elements) {
                const text = element.textContent.trim();
                if (text.match(/№\s*\d+/) && element.className.includes('num')) {
                    numberElement = element;
                    break;
                }
            }
        }
        
        if (numberElement) {
            const numberMatch = numberElement.textContent.match(/№\s*(\d+)/);
            if (numberMatch) {
                this.data.announcementNumber = numberMatch[1];
                console.log('✅ Номер объявления:', this.data.announcementNumber);
                return;
            }
        }
        
        // Поиск в URL или других местах
        const urlMatch = window.location.href.match(/\/(\d+)$/);
        if (urlMatch) {
            this.data.announcementNumber = urlMatch[1];
            console.log('✅ Номер объявления из URL:', this.data.announcementNumber);
            return;
        }
        
        console.log('❌ Номер объявления не найден');
    }

    // Парсинг заказчика
    parseSamrukCustomer() {
        // Основной поиск по структуре с jhitranslate="main.dialog.customer"
        const customerElements = document.querySelectorAll('.m-infoblock__layout');
        for (let element of customerElements) {
            const titleElement = element.querySelector('.m-infoblock__title[jhitranslate="main.dialog.customer"]');
            if (titleElement) {
                // Берем текст элемента без заголовка
                let customerText = element.textContent.replace(titleElement.textContent, '').trim();
                if (customerText) {
                    this.data.customerName = customerText;
                    console.log('✅ Заказчик (основной поиск):', this.data.customerName);
                    return;
                }
            }
        }
        
        // Альтернативный поиск по тексту "Заказчик"
        for (let element of customerElements) {
            const titleElement = element.querySelector('.m-infoblock__title');
            if (titleElement && titleElement.textContent.includes('Заказчик')) {
                let customerText = element.textContent.replace(titleElement.textContent, '').trim();
                if (customerText) {
                    this.data.customerName = customerText;
                    console.log('✅ Заказчик (альтернативный поиск):', this.data.customerName);
                    return;
                }
            }
        }
        
        // Поиск в примере: Акционерное общество "Казахтелеком"
        const pageText = document.body.textContent;
        const customerMatch = pageText.match(/Акционерное общество[^\.]+/i) || 
                            pageText.match(/АО "[^"]+"/g) ||
                            pageText.match(/ТОО "[^"]+"/g);
        if (customerMatch) {
            this.data.customerName = customerMatch[0].trim();
            console.log('✅ Заказчик (поиск по паттерну):', this.data.customerName);
            return;
        }
        
        console.log('❌ Заказчик не найден');
    }

    // Парсинг метода закупки
    parseSamrukPurchaseMethod() {
        // Основной поиск по структуре с jhitranslate="main.dialog.monthPurchase"
        const methodElements = document.querySelectorAll('.m-infoblock__layout');
        for (let element of methodElements) {
            const titleElement = element.querySelector('.m-infoblock__title[jhitranslate="main.dialog.monthPurchase"]');
            if (titleElement) {
                let methodText = element.textContent.replace(titleElement.textContent, '').trim();
                if (methodText) {
                    this.data.purchaseMethod = methodText;
                    console.log('✅ Метод закупки (основной поиск):', this.data.purchaseMethod);
                    return;
                }
            }
        }
        
        // Альтернативный поиск по тексту "МЕТОД ЗАКУПКИ"
        for (let element of methodElements) {
            const titleElement = element.querySelector('.m-infoblock__title');
            if (titleElement && titleElement.textContent.includes('МЕТОД ЗАКУПКИ')) {
                let methodText = element.textContent.replace(titleElement.textContent, '').trim();
                if (methodText) {
                    this.data.purchaseMethod = methodText;
                    console.log('✅ Метод закупки (альтернативный поиск):', this.data.purchaseMethod);
                    return;
                }
            }
        }
        
        // Поиск по известным методам закупки
        const pageText = document.body.textContent;
        const methods = [
            'Открытый тендер на понижение',
            'Запрос ценовых предложений на понижение',
            'Открытый конкурс',
            'Двухэтапный тендер'
        ];
        
        for (let method of methods) {
            if (pageText.includes(method)) {
                this.data.purchaseMethod = method;
                console.log('✅ Метод закупки (поиск по паттерну):', this.data.purchaseMethod);
                return;
            }
        }
        
        console.log('❌ Метод закупки не найден');
    }

    // Парсинг дат торгов (улучшенная версия для разных форматов)
    parseSamrukTradingDates() {
        console.log('🔍 Начинаем парсинг дат торгов для Самрук Казына...');
        
        // Просто ищем все элементы с датами и берем их текст как есть
        const dateElements = document.querySelectorAll('.m-rangebox__date');
        console.log(`Найдено элементов с датами: ${dateElements.length}`);
        
        const foundDates = [];
        
        dateElements.forEach((element, index) => {
            const dateText = element.textContent.trim();
            console.log(`Элемент ${index}: "${dateText}"`);
            
            // Проверяем разные форматы дат
            let isValidDate = false;
            
            // Формат 1: дд.мм.гггг чч:мм:сс (например, "02.07.2025 10:00:00")
            if (dateText.match(/^\d{2}\.\d{2}\.\d{4} \d{2}:\d{2}:\d{2}$/)) {
                isValidDate = true;
                console.log(`✅ Найдена дата в формате дд.мм.гггг: "${dateText}"`);
            }
            
            // Формат 2: дата с русскими названиями месяцев
            if (dateText.length > 5 && /\d/.test(dateText) && 
                (dateText.includes('января') || dateText.includes('февраля') || dateText.includes('марта') ||
                 dateText.includes('апреля') || dateText.includes('мая') || dateText.includes('июня') ||
                 dateText.includes('июля') || dateText.includes('августа') || dateText.includes('сентября') ||
                 dateText.includes('октября') || dateText.includes('ноября') || dateText.includes('декабря'))) {
                isValidDate = true;
                console.log(`✅ Найдена дата с русским названием месяца: "${dateText}"`);
            }
            
            // Формат 3: ISO формат или другие числовые форматы
            if (dateText.match(/\d{4}-\d{2}-\d{2}/) || dateText.match(/\d{2}-\d{2}-\d{4}/)) {
                isValidDate = true;
                console.log(`✅ Найдена дата в ISO или числовом формате: "${dateText}"`);
            }
            
            // Формат 4: просто проверяем, что есть цифры и длина разумная
            if (!isValidDate && dateText.length >= 8 && dateText.length <= 30 && /\d{2,4}/.test(dateText)) {
                isValidDate = true;
                console.log(`✅ Найдена дата по общему формату: "${dateText}"`);
            }
            
            if (isValidDate) {
                foundDates.push(dateText);
                console.log(`✅ Дата ${index} добавлена в массив: "${dateText}"`);
            } else {
                console.log(`❌ Дата ${index} не прошла проверку: "${dateText}"`);
            }
        });
        
        console.log(`📊 Итого найдено дат: ${foundDates.length}`);
        
        // Сохраняем даты как массив
        if (foundDates.length > 0) {
            this.data.tradingDates = foundDates;
            console.log('✅ Даты торгов сохранены как массив:', foundDates);
            
            // Для совместимости также назначаем первые две даты
            if (foundDates.length >= 1) {
                this.data.startDate = foundDates[0];
                console.log('✅ Дата начала торгов:', foundDates[0]);
            }
            if (foundDates.length >= 2) {
                this.data.endDate = foundDates[1];
                console.log('✅ Дата окончания торгов:', foundDates[1]);
            }
        } else {
            console.log('❌ Даты торгов не найдены');
            
            // Дополнительный поиск дат в тексте страницы
            console.log('🔍 Пробуем найти даты в тексте страницы...');
            const pageText = document.body.textContent;
            
            // Ищем даты в формате дд.мм.гггг
            const dateMatches = pageText.match(/\d{2}\.\d{2}\.\d{4}(?:\s+\d{2}:\d{2}:\d{2})?/g);
            if (dateMatches && dateMatches.length > 0) {
                console.log('✅ Найдены даты в тексте страницы:', dateMatches);
                this.data.tradingDates = dateMatches;
                this.data.startDate = dateMatches[0];
                if (dateMatches.length > 1) {
                    this.data.endDate = dateMatches[1];
                }
            }
        }
        
        console.log('🎯 Итоговые даты торгов:');
        console.log('Массив дат:', this.data.tradingDates || []);
        console.log('Дата начала:', this.data.startDate || 'не найдена');
        console.log('Дата окончания:', this.data.endDate || 'не найдена');
    }

    // Парсинг лотов
    parseSamrukLots() {
        console.log('Начинаем парсинг лотов...');
        
        // Ищем все аккордеоны с лотами
        const lotElements = document.querySelectorAll('.m-accordion__header');
        const lots = [];
        
        console.log(`Найдено потенциальных лотов: ${lotElements.length}`);
        
        lotElements.forEach((lotElement, index) => {
            const lot = {};
            
            console.log(`\n--- Парсинг лота ${index + 1} ---`);
            
            // Номер и название лота из .m-label
            const labelElement = lotElement.querySelector('.m-label');
            if (labelElement) {
                const labelText = labelElement.textContent.trim();
                lot.lotNumber = labelText;
                console.log(`✅ Номер лота: "${labelText}"`);
            }
            
            // Наименование товара из .m-accordion__description .m-span--big
            const descriptionElement = lotElement.querySelector('.m-accordion__description .m-span--big');
            if (descriptionElement) {
                const productText = descriptionElement.textContent.trim();
                lot.productName = productText;
                console.log(`✅ Наименование товара: "${productText}"`);
            }
            
            // Дополнительное описание из .m-accordion__title (следующий элемент)
            const additionalDescElement = lotElement.querySelector('.m-accordion__title');
            if (additionalDescElement && additionalDescElement.textContent.trim()) {
                const additionalText = additionalDescElement.textContent.trim();
                if (lot.productName && additionalText !== lot.productName) {
                    lot.productName += ` - ${additionalText}`;
                    console.log(`✅ Дополнительное описание добавлено: "${additionalText}"`);
                }
            }
            
            // Поиск количества и суммы в колонках
            const columns = lotElement.querySelectorAll('.m-accordion__col');
            console.log(`Найдено колонок: ${columns.length}`);
            
            columns.forEach((col, colIndex) => {
                const titleElement = col.querySelector('.m-accordion__title');
                const descElement = col.querySelector('.m-accordion__description');
                
                if (titleElement && descElement) {
                    const title = titleElement.textContent.trim();
                    const value = descElement.textContent.trim();
                    
                    console.log(`Колонка ${colIndex}: "${title}" = "${value}"`);
                    
                    // Количество
                    if (title.includes('Количество') || title.includes('quantity')) {
                        lot.quantity = value;
                        console.log(`✅ Количество найдено: "${value}"`);
                    }
                    
                    // Цена и сумма
                    if (title.includes('Цена за ед./Сумма') || title.includes('pricePerUnitShortAndSum')) {
                        // В этом поле может быть две строки: цена за единицу и общая сумма
                        const spans = descElement.querySelectorAll('span');
                        if (spans.length >= 2) {
                            const unitPrice = spans[0].textContent.trim();
                            const totalSum = spans[1].textContent.trim();
                            
                            lot.unitPrice = unitPrice;
                            lot.lotSum = totalSum;
                            console.log(`✅ Цена за единицу: "${unitPrice}"`);
                            console.log(`✅ Общая сумма: "${totalSum}"`);
                        } else if (spans.length === 1) {
                            lot.lotSum = spans[0].textContent.trim();
                            console.log(`✅ Сумма лота: "${lot.lotSum}"`);
                        } else {
                            // Если нет span'ов, берем весь текст
                            lot.lotSum = value;
                            console.log(`✅ Сумма лота (общий текст): "${value}"`);
                        }
                    }
                    
                    // Единица измерения
                    if (title.includes('Ед. измерения') || title.includes('mkeiShort')) {
                        lot.unit = value;
                        console.log(`✅ Единица измерения: "${value}"`);
                    }
                    
                    // Место поставки
                    if (title.includes('МЕСТО ПОСТАВКИ') || title.includes('deilveryLocation')) {
                        lot.deliveryLocation = value;
                        console.log(`✅ Место поставки: "${value}"`);
                    }
                    
                    // Сроки
                    if (title.includes('СРОКИ') || title.includes('terms')) {
                        lot.terms = value;
                        console.log(`✅ Сроки: "${value}"`);
                    }
                }
            });
            
            // Добавляем лот только если есть существенные данные
            if (lot.lotNumber || lot.productName || lot.quantity || lot.lotSum) {
                lots.push(lot);
                console.log(`✅ Лот ${index + 1} добавлен в список`);
            } else {
                console.log(`❌ Лот ${index + 1} пропущен - недостаточно данных`);
            }
        });
        
        this.data.lots = lots;
        console.log(`\n🎉 Всего лотов найдено и добавлено: ${lots.length}`);
        
        // Логируем итоговые данные по лотам
        lots.forEach((lot, index) => {
            console.log(`\nЛот ${index + 1}:`);
            console.log(`  Номер: ${lot.lotNumber || 'не указан'}`);
            console.log(`  Наименование: ${lot.productName || 'не указано'}`);
            console.log(`  Количество: ${lot.quantity || 'не указано'}`);
            console.log(`  Сумма: ${lot.lotSum || 'не указана'}`);
        });
    }

    // Парсинг деталей лота (улучшенная версия)
    parseLotDetails(selectedLotId = null) {
        console.log('🔍 Начинаем улучшенный парсинг деталей лота для:', selectedLotId || 'основная страница');
        
        // Логируем текущий URL для понимания типа страницы
        console.log('📍 Текущий URL:', window.location.href);
        
        // Проверяем, находимся ли мы на странице детального просмотра лота
        const isDetailPage = window.location.href.includes('/announce/') || 
                            document.querySelector('table td:first-child:contains("Лот №")') ||
                            document.querySelector('table th:contains("Лот №")');
        
        if (isDetailPage) {
            console.log('📋 Обнаружена страница детального просмотра лота');
        } else {
            console.log('📋 Обнаружена страница списка лотов');
        }
        
        // Сначала ищем полный номер лота (с суффиксом)
        this.parseLotNumber(selectedLotId);
        
        // Если передан selectedLotId, сохраняем его как выбранный лот
        if (selectedLotId) {
            this.selectedLotId = selectedLotId;
            console.log('🎯 Установлен выбранный лот:', selectedLotId);
        }
        
        // Сначала пробуем найти данные в разных местах на странице
        this.parseCustomerFromPage(selectedLotId);
        this.parseProductFromPage(selectedLotId);
        this.parseQuantityFromPage(selectedLotId);
        this.parseAnnouncedSumFromPage(selectedLotId);
        
        // Логируем найденные данные для отладки
        console.log('🎯 Найденные данные после парсинга:');
        console.log('- Номер лота:', this.data.lotNumber || 'не найден');
        console.log('- Заказчик:', this.data.customerName || 'не найден');
        console.log('- Товар:', this.data.productName || 'не найден');
        console.log('- Количество:', this.data.quantity || 'не найдено');
        console.log('- Сумма:', this.data.announcedSum || 'не найдена');
        console.log('- Выбранный лот ID:', selectedLotId || 'не указан');
        
        console.log('🎉 Парсинг деталей лота завершен');
    }

    // Парсинг заказчика из разных мест на странице (исправленная версия)
    parseCustomerFromPage(selectedLotId = null) {
        console.log('🔍 Ищем заказчика для лота:', selectedLotId || 'основная страница');
        
        // Если указан selectedLotId, ищем строку в таблице с этим лотом
        if (selectedLotId) {
            console.log('🎯 Поиск данных для конкретного лота:', selectedLotId);
            
            // Ищем таблицу с лотами (структура как в примере)
            const lotTables = document.querySelectorAll('table.table-bordered, table');
            for (let table of lotTables) {
                const rows = table.querySelectorAll('tr');
                for (let row of rows) {
                    // Ищем ссылку с нужным data-lot-id
                    const lotLink = row.querySelector(`a[data-lot-id="${selectedLotId}"]`);
                    if (lotLink) {
                        console.log('✅ Найдена строка с выбранным лотом');
                        const cells = row.querySelectorAll('td');
                        
                        // Анализируем структуру таблицы и ищем колонку "Заказчик"
                        const headerRow = table.querySelector('tr');
                        const headers = headerRow ? headerRow.querySelectorAll('th') : [];
                        
                        let customerColumnIndex = -1;
                        headers.forEach((header, index) => {
                            const headerText = header.textContent.trim();
                            if (headerText === 'Заказчик' || headerText.includes('Заказчик')) {
                                customerColumnIndex = index;
                                console.log(`✅ Найдена колонка "Заказчик" с индексом: ${index}`);
                            }
                        });
                        
                        // Если нашли колонку заказчика, берем данные из этой ячейки
                        if (customerColumnIndex >= 0 && cells[customerColumnIndex]) {
                            const customerName = cells[customerColumnIndex].textContent.trim();
                            if (customerName.length > 5) {
                                this.data.customerName = customerName;
                                console.log('✅ Заказчик найден для выбранного лота:', customerName);
                                return;
                            }
                        }
                        
                        // Если не нашли по индексу, ищем по содержимому (для случаев неточного соответствия)
                        for (let i = 0; i < cells.length; i++) {
                            const cellText = cells[i].textContent.trim();
                            // Проверяем, что это похоже на название организации
                            if (cellText.length > 20 && 
                                (cellText.includes('учреждение') || 
                                 cellText.includes('школа') || 
                                 cellText.includes('больница') ||
                                 cellText.includes('управление') ||
                                 cellText.includes('департамент') ||
                                 cellText.includes('акимат') ||
                                 cellText.includes('ГУ ') ||
                                 cellText.includes('КГУ ') ||
                                 cellText.includes('АО ') ||
                                 cellText.includes('ТОО '))) {
                                this.data.customerName = cellText;
                                console.log('✅ Заказчик найден по содержимому для выбранного лота:', cellText);
                                return;
                            }
                        }
                    }
                }
            }
        }
        
        let searchScope = selectedLotId ? 
            (document.querySelector(`[data-lot-id="${selectedLotId}"]`) || document) : 
            document;
        
        // Поиск в таблицах по точным ключевым словам (общий поиск)
        const tables = searchScope.querySelectorAll('table');
        for (let table of tables) {
            const rows = table.querySelectorAll('tr');
            for (let row of rows) {
                const cells = row.querySelectorAll('td, th');
                if (cells.length >= 2) {
                    const firstCell = cells[0].textContent.trim();
                    const secondCell = cells[1].textContent.trim();
                    
                    console.log(`Проверяем поле заказчика: "${firstCell}" = "${secondCell}"`);
                    
                    // Точные совпадения для заказчика (ПРИОРИТЕТ 1)
                    if (firstCell === 'Наименование заказчика' || 
                        firstCell.includes('Наименование заказчика')) {
                        
                        // Берем любое непустое значение из этого поля
                        if (secondCell.length > 5) {
                            this.data.customerName = secondCell;
                            console.log('✅ Заказчик найден в поле "Наименование заказчика":', secondCell);
                            return;
                        }
                    }
                    
                    // Дополнительные варианты (ПРИОРИТЕТ 2)
                    if (firstCell === 'Заказчик' || 
                        firstCell === 'Организатор' ||
                        firstCell === 'Заказчик:' ||
                        firstCell === 'Организатор:') {
                        
                        // Берем любое непустое значение из этого поля
                        if (secondCell.length > 5) {
                            this.data.customerName = secondCell;
                            console.log('✅ Заказчик найден в таблице:', secondCell);
                            return;
                        }
                    }
                }
            }
        }
        
        // Поиск в div элементах с точными паттернами
        const divs = document.querySelectorAll('div');
        for (let div of divs) {
            const text = div.textContent.trim();
            
            // Поиск с двоеточием
            if (text.includes('Заказчик:') || text.includes('Организатор:')) {
                const match = text.match(/(?:Заказчик|Организатор):\s*(.+?)(?:\n|$)/);
                if (match && match[1]) {
                    const customer = match[1].trim();
                    if (customer.length > 5) {
                        this.data.customerName = customer;
                        console.log('✅ Заказчик найден в div:', customer);
                        return;
                    }
                }
            }
        }
        
        // Поиск организаций по улучшенным паттернам
        const pageText = document.body.textContent;
        
        // Государственные учреждения
        const govPatterns = [
            /ГУ\s+"[^"]+"/g,
            /Государственное учреждение\s+"[^"]+"/g,
            /КГУ\s+"[^"]+"/g,
            /Коммунальное государственное учреждение\s+"[^"]+"/g
        ];
        
        for (let pattern of govPatterns) {
            const matches = pageText.match(pattern);
            if (matches && matches.length > 0) {
                // Берем самое полное название
                const longestMatch = matches.reduce((a, b) => a.length > b.length ? a : b);
                this.data.customerName = longestMatch.trim();
                console.log('✅ Заказчик найден по паттерну ГУ:', longestMatch.trim());
                return;
            }
        }
        
        // Акционерные общества и ТОО
        const companyPatterns = [
            /АО\s+"[^"]+"/g,
            /Акционерное общество\s+"[^"]+"/g,
            /ТОО\s+"[^"]+"/g,
            /Товарищество с ограниченной ответственностью\s+"[^"]+"/g
        ];
        
        for (let pattern of companyPatterns) {
            const matches = pageText.match(pattern);
            if (matches && matches.length > 0) {
                const longestMatch = matches.reduce((a, b) => a.length > b.length ? a : b);
                this.data.customerName = longestMatch.trim();
                console.log('✅ Заказчик найден по паттерну компании:', longestMatch.trim());
                return;
            }
        }
        
        // Образовательные учреждения
        const educationPatterns = [
            /(?:Школа-гимназия|Школа|Гимназия|Лицей)\s+[^\.]{10,100}/g,
            /(?:Университет|Институт|Колледж)\s+[^\.]{10,100}/g
        ];
        
        for (let pattern of educationPatterns) {
            const matches = pageText.match(pattern);
            if (matches && matches.length > 0) {
                const longestMatch = matches.reduce((a, b) => a.length > b.length ? a : b);
                this.data.customerName = longestMatch.trim();
                console.log('✅ Заказчик найден по образовательному паттерну:', longestMatch.trim());
                return;
            }
        }
        
        // Медицинские учреждения
        const medicalPatterns = [
            /(?:Больница|Поликлиника|Медицинский центр)\s+[^\.]{10,100}/g,
            /(?:Городская|Областная|Районная)\s+(?:больница|поликлиника)\s+[^\.]{10,100}/g
        ];
        
        for (let pattern of medicalPatterns) {
            const matches = pageText.match(pattern);
            if (matches && matches.length > 0) {
                const longestMatch = matches.reduce((a, b) => a.length > b.length ? a : b);
                this.data.customerName = longestMatch.trim();
                console.log('✅ Заказчик найден по медицинскому паттерну:', longestMatch.trim());
                return;
            }
        }
        
        // Управления и департаменты
        const adminPatterns = [
            /(?:Управление|Департамент|Комитет|Министерство)\s+[^\.]{10,150}/g,
            /(?:Акимат|Областное управление)\s+[^\.]{10,150}/g
        ];
        
        for (let pattern of adminPatterns) {
            const matches = pageText.match(pattern);
            if (matches && matches.length > 0) {
                const longestMatch = matches.reduce((a, b) => a.length > b.length ? a : b);
                this.data.customerName = longestMatch.trim();
                console.log('✅ Заказчик найден по административному паттерну:', longestMatch.trim());
                return;
            }
        }
        
        console.log('❌ Заказчик не найден');
    }

    // Парсинг наименования товара (исправленная версия)
    parseProductFromPage(selectedLotId = null) {
        console.log('🔍 Ищем наименование товара для лота:', selectedLotId || 'основная страница');
        
        // Если указан selectedLotId, ищем строку в таблице с этим лотом
        if (selectedLotId) {
            console.log('🎯 Поиск наименования товара для конкретного лота:', selectedLotId);
            
            // Ищем таблицу с лотами
            const lotTables = document.querySelectorAll('table.table-bordered, table');
            for (let table of lotTables) {
                const rows = table.querySelectorAll('tr');
                for (let row of rows) {
                    // Ищем ссылку с нужным data-lot-id
                    const lotLink = row.querySelector(`a[data-lot-id="${selectedLotId}"]`);
                    if (lotLink) {
                        console.log('✅ Найдена строка с выбранным лотом для товара');
                        const cells = row.querySelectorAll('td');
                        
                        // Анализируем структуру таблицы и ищем колонки с наименованием
                        const headerRow = table.querySelector('tr');
                        const headers = headerRow ? headerRow.querySelectorAll('th') : [];
                        
                        let nameColumnIndex = -1;
                        let descriptionColumnIndex = -1;
                        
                        headers.forEach((header, index) => {
                            const headerText = header.textContent.trim();
                            if (headerText === 'Наименование' || headerText.includes('Наименование')) {
                                nameColumnIndex = index;
                                console.log(`✅ Найдена колонка "Наименование" с индексом: ${index}`);
                            }
                            if (headerText === 'Дополнительная характеристика' || headerText.includes('характеристика')) {
                                descriptionColumnIndex = index;
                                console.log(`✅ Найдена колонка "Дополнительная характеристика" с индексом: ${index}`);
                            }
                        });
                        
                        // Собираем наименование товара
                        let productName = '';
                        
                        // Основное наименование
                        if (nameColumnIndex >= 0 && cells[nameColumnIndex]) {
                            productName = cells[nameColumnIndex].textContent.trim();
                        }
                        
                        // Дополнительная характеристика
                        if (descriptionColumnIndex >= 0 && cells[descriptionColumnIndex]) {
                            const description = cells[descriptionColumnIndex].textContent.trim();
                            if (description && description.length > 0) {
                                productName = productName ? `${productName} - ${description}` : description;
                            }
                        }
                        
                        if (productName && productName.length > 3) {
                            this.data.productName = productName;
                            console.log('✅ Наименование товара найдено для выбранного лота:', productName);
                            return;
                        }
                    }
                }
            }
        }
        
        let searchScope = selectedLotId ? 
            (document.querySelector(`[data-lot-id="${selectedLotId}"]`) || document) : 
            document;
        
        // Поиск в таблицах по специфическим ключевым словам
        const tables = searchScope.querySelectorAll('table');
        for (let table of tables) {
            const rows = table.querySelectorAll('tr');
            for (let row of rows) {
                const cells = row.querySelectorAll('td, th');
                if (cells.length >= 2) {
                    const firstCell = cells[0].textContent.trim();
                    const secondCell = cells[1].textContent.trim();
                    
                    console.log(`Проверяем поле товара: "${firstCell}" = "${secondCell}"`);
                    
                    // Ищем конкретные поля для товара/услуги (ПРИОРИТЕТ 1)
                    if (firstCell.includes('Наименование заказчика')) {
                        // Пропускаем поле заказчика
                        continue;
                    }
                    
                    if (firstCell.includes('Наименование товара') || 
                        firstCell.includes('Предмет закупки') || 
                        firstCell.includes('Наименование услуг') ||
                        firstCell.includes('Описание предмета') ||
                        firstCell.includes('Дополнительная характеристика') ||
                        firstCell.includes('Краткая характеристика') ||
                        firstCell.includes('Наименование ТРУ')) {
                        
                        // Берем любое непустое значение из этого поля
                        if (secondCell.length > 5) {
                            this.data.productName = secondCell;
                            console.log('✅ Наименование товара найдено в таблице:', secondCell);
                            return;
                        }
                    }
                }
            }
        }
        
        // Поиск товаров/услуг по ключевым словам в тексте
        const pageText = document.body.textContent;
        
        // Паттерны для поиска товаров
        const productPatterns = [
            /(?:Предмет закупки|Товар|Услуга|Работа):\s*([^\.]{10,200})/i,
            /(?:Закупаемый товар|Наименование закупки):\s*([^\.]{10,200})/i,
            /(?:Описание|Характеристика):\s*([^\.]{10,200})/i
        ];
        
        for (let pattern of productPatterns) {
            const match = pageText.match(pattern);
            if (match && match[1]) {
                const product = match[1].trim();
                // Проверяем, что это не организация
                if (!product.includes('учреждение') && 
                    !product.includes('управления') &&
                    !product.includes('отдела') &&
                    !product.includes('департамент')) {
                    
                    this.data.productName = product;
                    console.log('✅ Наименование товара найдено по паттерну:', product);
                    return;
                }
            }
        }
        
        // Поиск в списках товаров (исключаем организации)
        const lists = document.querySelectorAll('ul, ol');
        for (let list of lists) {
            const items = list.querySelectorAll('li');
            for (let item of items) {
                const text = item.textContent.trim();
                if (text.length > 5 && text.length < 300 && 
                    !text.includes('учреждение') && 
                    !text.includes('управления') &&
                    !text.includes('отдела') &&
                    !text.includes('департамент') &&
                    !text.includes('комитет') &&
                    !text.includes('министерство') &&
                    !text.includes('ГУ ') &&
                    !text.includes('АО ') &&
                    !text.includes('ТОО ')) {
                    
                    this.data.productName = text;
                    console.log('✅ Наименование товара найдено в списке:', text);
                    return;
                }
            }
        }
        
        // Поиск в div элементах с исключением организационных структур
        const productDivs = document.querySelectorAll('div[class*="product"], div[class*="item"], div[class*="description"]');
        for (let div of productDivs) {
            const text = div.textContent.trim();
            if (text.length > 5 && text.length < 300 && 
                !text.includes('учреждение') && 
                !text.includes('управления') &&
                !text.includes('отдела') &&
                !text.includes('школа') &&
                !text.includes('гимназия') &&
                !text.includes('университет') &&
                !text.includes('институт') &&
                !text.includes('Заказчик') && 
                !text.includes('Организатор')) {
                
                this.data.productName = text;
                console.log('✅ Наименование товара найдено в div:', text.substring(0, 100) + '...');
                return;
            }
        }
        
        console.log('❌ Наименование товара не найдено');
    }

    // Парсинг количества
    parseQuantityFromPage(selectedLotId = null) {
        console.log('🔍 Ищем количество товара для лота:', selectedLotId || 'основная страница');
        
        // Если указан selectedLotId, ищем строку в таблице с этим лотом
        if (selectedLotId) {
            console.log('🎯 Поиск количества для конкретного лота:', selectedLotId);
            
            // Ищем таблицу с лотами
            const lotTables = document.querySelectorAll('table.table-bordered, table');
            for (let table of lotTables) {
                const rows = table.querySelectorAll('tr');
                for (let row of rows) {
                    // Ищем ссылку с нужным data-lot-id
                    const lotLink = row.querySelector(`a[data-lot-id="${selectedLotId}"]`);
                    if (lotLink) {
                        console.log('✅ Найдена строка с выбранным лотом для количества');
                        const cells = row.querySelectorAll('td');
                        
                        // Анализируем структуру таблицы и ищем колонку "Кол-во"
                        const headerRow = table.querySelector('tr');
                        const headers = headerRow ? headerRow.querySelectorAll('th') : [];
                        
                        let quantityColumnIndex = -1;
                        let unitColumnIndex = -1;
                        
                        headers.forEach((header, index) => {
                            const headerText = header.textContent.trim();
                            if (headerText === 'Кол-во' || headerText.includes('Количество')) {
                                quantityColumnIndex = index;
                                console.log(`✅ Найдена колонка "Кол-во" с индексом: ${index}`);
                            }
                            if (headerText === 'Ед. изм.' || headerText.includes('Единица')) {
                                unitColumnIndex = index;
                                console.log(`✅ Найдена колонка "Ед. изм." с индексом: ${index}`);
                            }
                        });
                        
                        // Количество
                        if (quantityColumnIndex >= 0 && cells[quantityColumnIndex]) {
                            const quantity = cells[quantityColumnIndex].textContent.trim();
                            if (quantity && quantity.length > 0) {
                                this.data.quantity = quantity;
                                console.log('✅ Количество найдено для выбранного лота:', quantity);
                            }
                        }
                        
                        // Единица измерения
                        if (unitColumnIndex >= 0 && cells[unitColumnIndex]) {
                            const unit = cells[unitColumnIndex].textContent.trim();
                            if (unit && unit.length > 0) {
                                this.data.unit = unit;
                                console.log('✅ Единица измерения найдена для выбранного лота:', unit);
                            }
                        }
                        
                        if (this.data.quantity) {
                            return;
                        }
                    }
                }
            }
        }
        
        // Поиск в таблицах (общий поиск)
        const tables = document.querySelectorAll('table');
        for (let table of tables) {
            const rows = table.querySelectorAll('tr');
            for (let row of rows) {
                const cells = row.querySelectorAll('td, th');
                if (cells.length >= 2) {
                    const firstCell = cells[0].textContent.trim();
                    const secondCell = cells[1].textContent.trim();
                    
                    if (firstCell.includes('Количество') || firstCell.includes('Кол-во') || 
                        firstCell.includes('Объем') || firstCell.includes('Штук')) {
                        // Извлекаем числовое значение
                        const numMatch = secondCell.match(/(\d+(?:[.,]\d+)?)/);
                        if (numMatch) {
                            this.data.quantity = numMatch[1];
                            console.log('✅ Количество найдено в таблице:', numMatch[1]);
                            return;
                        }
                    }
                }
            }
        }
        
        // Поиск по паттернам в тексте
        const pageText = document.body.textContent;
        const quantityPatterns = [
            /Количество:\s*(\d+(?:[.,]\d+)?)/i,
            /Кол-во:\s*(\d+(?:[.,]\d+)?)/i,
            /(\d+(?:[.,]\d+)?)\s*(?:шт|штук|единиц|литр|кг|м|м²|м³)/i
        ];
        
        for (let pattern of quantityPatterns) {
            const match = pageText.match(pattern);
            if (match) {
                this.data.quantity = match[1];
                console.log('✅ Количество найдено по паттерну:', match[1]);
                return;
            }
        }
        
        console.log('❌ Количество товара не найдено');
    }

    // Парсинг объявленной суммы (исправленная версия для v3bl.goszakup)
    parseAnnouncedSumFromPage(selectedLotId = null) {
        console.log('🔍 Ищем объявленную/запланированную сумму для лота:', selectedLotId || 'основная страница');
        
        // Если указан selectedLotId, ищем строку в таблице с этим лотом
        if (selectedLotId) {
            console.log('🎯 Поиск суммы для конкретного лота:', selectedLotId);
            
            // Ищем таблицу с лотами
            const lotTables = document.querySelectorAll('table.table-bordered, table');
            for (let table of lotTables) {
                const rows = table.querySelectorAll('tr');
                for (let row of rows) {
                    // Ищем ссылку с нужным data-lot-id
                    const lotLink = row.querySelector(`a[data-lot-id="${selectedLotId}"]`);
                    if (lotLink) {
                        console.log('✅ Найдена строка с выбранным лотом для суммы');
                        const cells = row.querySelectorAll('td');
                        
                        // Анализируем структуру таблицы и ищем колонки с суммами
                        const headerRow = table.querySelector('tr');
                        const headers = headerRow ? headerRow.querySelectorAll('th') : [];
                        
                        let plannedSumColumnIndex = -1;
                        let unitPriceColumnIndex = -1;
                        let sum1ColumnIndex = -1;
                        let sum2ColumnIndex = -1;
                        let sum3ColumnIndex = -1;
                        
                        headers.forEach((header, index) => {
                            const headerText = header.textContent.trim();
                            if (headerText === 'Плановая сумма' || headerText.includes('Плановая')) {
                                plannedSumColumnIndex = index;
                                console.log(`✅ Найдена колонка "Плановая сумма" с индексом: ${index}`);
                            }
                            if (headerText === 'Цена за ед.' || headerText.includes('Цена за')) {
                                unitPriceColumnIndex = index;
                                console.log(`✅ Найдена колонка "Цена за ед." с индексом: ${index}`);
                            }
                            if (headerText === 'Сумма 1 год') {
                                sum1ColumnIndex = index;
                                console.log(`✅ Найдена колонка "Сумма 1 год" с индексом: ${index}`);
                            }
                            if (headerText === 'Сумма 2 год') {
                                sum2ColumnIndex = index;
                                console.log(`✅ Найдена колонка "Сумма 2 год" с индексом: ${index}`);
                            }
                            if (headerText === 'Сумма 3 год') {
                                sum3ColumnIndex = index;
                                console.log(`✅ Найдена колонка "Сумма 3 год" с индексом: ${index}`);
                            }
                        });
                        
                        // Приоритет 1: Плановая сумма
                        if (plannedSumColumnIndex >= 0 && cells[plannedSumColumnIndex]) {
                            const plannedSum = cells[plannedSumColumnIndex].textContent.trim();
                            const numMatch = plannedSum.match(/(\d+(?:\s*\d+)*(?:[.,]\d+)?)/);
                            if (numMatch) {
                                this.data.announcedSum = numMatch[1].replace(/\s/g, '');
                                console.log('✅ Плановая сумма найдена для выбранного лота:', this.data.announcedSum);
                                
                                // Также сохраняем цену за единицу если есть
                                if (unitPriceColumnIndex >= 0 && cells[unitPriceColumnIndex]) {
                                    const unitPrice = cells[unitPriceColumnIndex].textContent.trim();
                                    this.data.unitPrice = unitPrice;
                                    console.log('✅ Цена за единицу найдена:', unitPrice);
                                }
                                return;
                            }
                        }
                        
                        // Приоритет 2: Сумма по годам
                        let totalYearSum = 0;
                        let foundYearSums = false;
                        
                        [sum1ColumnIndex, sum2ColumnIndex, sum3ColumnIndex].forEach((columnIndex, yearIndex) => {
                            if (columnIndex >= 0 && cells[columnIndex]) {
                                const yearSum = cells[columnIndex].textContent.trim();
                                const numMatch = yearSum.match(/(\d+(?:\s*\d+)*(?:[.,]\d+)?)/);
                                if (numMatch) {
                                    const sum = parseFloat(numMatch[1].replace(/\s/g, '').replace(',', '.'));
                                    totalYearSum += sum;
                                    foundYearSums = true;
                                    console.log(`✅ Сумма ${yearIndex + 1} год: ${sum}`);
                                }
                            }
                        });
                        
                        if (foundYearSums && totalYearSum > 0) {
                            this.data.announcedSum = totalYearSum.toString();
                            console.log('✅ Общая сумма по годам найдена для выбранного лота:', totalYearSum);
                            return;
                        }
                        
                        console.log('⚠️ Суммы в строке лота не найдены, проверим другие способы');
                    }
                }
            }
        }
        
        // Приоритетный поиск в таблицах по специфическим полям для goszakup
        const tables = document.querySelectorAll('table');
        for (let table of tables) {
            const rows = table.querySelectorAll('tr');
            for (let row of rows) {
                const cells = row.querySelectorAll('td, th');
                if (cells.length >= 2) {
                    const firstCell = cells[0].textContent.trim();
                    const secondCell = cells[1].textContent.trim();
                    
                    console.log(`Проверяем поле: "${firstCell}" = "${secondCell}"`);
                    
                    // Приоритет 1: Запланированная сумма (самое точное поле)
                    if (firstCell.includes('Запланированная сумма')) {
                        const numMatch = secondCell.match(/(\d+(?:\s*\d+)*(?:[.,]\d+)?)/);
                        if (numMatch) {
                            this.data.announcedSum = numMatch[1].replace(/\s/g, '');
                            console.log('✅ Запланированная сумма найдена в таблице:', this.data.announcedSum);
                            return;
                        }
                    }
                    
                    // Приоритет 2: Сумма за все годы или общая сумма
                    if (firstCell.includes('Сумма 1 год') && !this.data.announcedSum) {
                        // Ищем все строки с суммами по годам и складываем их
                        let totalSum = 0;
                        let foundYearSums = false;
                        
                        // Проходим по всем строкам таблицы для поиска сумм по годам
                        for (let yearRow of rows) {
                            const yearCells = yearRow.querySelectorAll('td, th');
                            if (yearCells.length >= 2) {
                                const yearFirstCell = yearCells[0].textContent.trim();
                                const yearSecondCell = yearCells[1].textContent.trim();
                                
                                if (yearFirstCell.match(/Сумма \d+ год/)) {
                                    console.log(`Найдена сумма по году: "${yearFirstCell}" = "${yearSecondCell}"`);
                                    const yearSumMatch = yearSecondCell.match(/(\d+(?:\s*\d+)*(?:[.,]\d+)?)/);
                                    if (yearSumMatch) {
                                        const yearSum = parseInt(yearSumMatch[1].replace(/\s/g, ''));
                                        totalSum += yearSum;
                                        foundYearSums = true;
                                        console.log(`✅ Добавлена сумма ${yearSum}, итого: ${totalSum}`);
                                    }
                                }
                            }
                        }
                        
                        if (foundYearSums && totalSum > 0) {
                            this.data.announcedSum = totalSum.toString();
                            console.log('✅ Общая сумма по годам найдена:', totalSum);
                            return;
                        }
                    }
                    
                    // Приоритет 3: Поиск поля с максимальной суммой среди основных полей
                    if ((firstCell.includes('Сумма') && !firstCell.includes('за единицу') && !firstCell.includes('Цена за единицу')) ||
                        firstCell.includes('Стоимость') || 
                        firstCell.includes('Бюджет') ||
                        firstCell.includes('Плановая') ||
                        firstCell.includes('Общая сумма') ||
                        firstCell.includes('Итоговая сумма')) {
                        
                        const numMatch = secondCell.match(/(\d+(?:\s*\d+)*(?:[.,]\d+)?)/);
                        if (numMatch) {
                            const sum = parseInt(numMatch[1].replace(/\s/g, ''));
                            // Сохраняем только если сумма больше уже найденной или если еще не найдена
                            if (!this.data.announcedSum || sum > parseInt(this.data.announcedSum)) {
                                this.data.announcedSum = sum.toString();
                                console.log(`✅ Обновлена объявленная сумма из поля "${firstCell}":`, sum);
                            }
                        }
                    }
                    
                    // Дополнительные проверки для goszakup
                    if (firstCell.includes('Авансовый платеж') || firstCell.includes('Размер авансового платежа')) {
                        const numMatch = secondCell.match(/(\d+(?:\s*\d+)*(?:[.,]\d+)?)/);
                        if (numMatch) {
                            this.data.advancePayment = numMatch[1].replace(/\s/g, '');
                            console.log('✅ Размер авансового платежа найден в таблице:', this.data.advancePayment);
                        }
                    }
                }
            }
        }
        
        // Если нашли сумму в таблице, возвращаемся
        if (this.data.announcedSum) {
            console.log('✅ Итоговая объявленная сумма:', this.data.announcedSum);
            return;
        }
        
        // Дополнительный поиск в тексте страницы по специфическим паттернам
        const pageText = document.body.textContent;
        
        // Поиск запланированной суммы в тексте
        const plannedSumPatterns = [
            /Запланированная сумма[:\s]*(\d+(?:\s*\d+)*(?:[.,]\d+)?)/i,
            /Объявленная сумма[:\s]*(\d+(?:\s*\d+)*(?:[.,]\d+)?)/i,
            /Общая сумма[:\s]*(\d+(?:\s*\d+)*(?:[.,]\d+)?)/i,
            /Итоговая сумма[:\s]*(\d+(?:\s*\d+)*(?:[.,]\d+)?)/i
        ];
        
        for (let pattern of plannedSumPatterns) {
            const match = pageText.match(pattern);
            if (match && match[1]) {
                const sum = match[1].replace(/\s/g, '');
                this.data.announcedSum = sum;
                console.log('✅ Объявленная сумма найдена в тексте по паттерну:', sum);
                return;
            }
        }
        
        // Поиск больших чисел с валютными обозначениями (только если ничего не найдено)
        const currencyPatterns = [
            /(\d{5,}(?:\s*\d+)*(?:[.,]\d+)?)\s*(?:тенге|₸|тг)/gi,
            /(\d{6,}(?:\s*\d+)*(?:[.,]\d+)?)\s*00/g // Числа с двумя нулями в конце (часто суммы)
        ];
        
        for (let pattern of currencyPatterns) {
            const matches = pageText.match(pattern);
            if (matches && matches.length > 0) {
                // Берем самое большое число
                const numbers = matches.map(match => {
                    const num = match.replace(/\D/g, '');
                    return parseInt(num);
                }).filter(num => num > 50000); // Фильтруем слишком маленькие суммы
                
                if (numbers.length > 0) {
                    const maxSum = Math.max(...numbers);
                    this.data.announcedSum = maxSum.toString();
                    console.log('✅ Объявленная сумма найдена по валютному паттерну:', maxSum);
                    return;
                }
            }
        }
        
        console.log('❌ Объявленная сумма не найдена');
    }

    // Дополнительный парсинг финансовых полей специально для goszakup
    parseGoszakupFinancialFields(selectedLotId = null) {
        console.log('🔍 Парсим финансовые поля для goszakup, лот:', selectedLotId || 'основная страница');
        
        const financialData = {};
        
        // Если указан selectedLotId, ищем финансовые данные для этого лота
        if (selectedLotId) {
            console.log('🎯 Поиск финансовых данных для конкретного лота:', selectedLotId);
            
            const lotTables = document.querySelectorAll('table.table-bordered, table');
            for (let table of lotTables) {
                const rows = table.querySelectorAll('tr');
                for (let row of rows) {
                    const lotLink = row.querySelector(`a[data-lot-id="${selectedLotId}"]`);
                    if (lotLink) {
                        console.log('✅ Найдена строка с выбранным лотом для финансовых данных');
                        const cells = row.querySelectorAll('td');
                        
                        const headerRow = table.querySelector('tr');
                        const headers = headerRow ? headerRow.querySelectorAll('th') : [];
                        
                        // Собираем все финансовые данные из строки лота
                        headers.forEach((header, index) => {
                            const headerText = header.textContent.trim();
                            const cellValue = cells[index] ? cells[index].textContent.trim() : '';
                            
                            if (headerText.includes('Плановая сумма') || headerText.includes('сумма')) {
                                const numMatch = cellValue.match(/(\d+(?:\s*\d+)*(?:[.,]\d+)?)/);
                                if (numMatch) {
                                    const cleanValue = numMatch[1].replace(/\s/g, '');
                                    if (headerText.includes('Плановая')) {
                                        financialData.plannedSum = cleanValue;
                                        console.log('💰 Плановая сумма из таблицы лотов:', cleanValue);
                                    } else if (headerText.includes('1 год')) {
                                        financialData.sum1Year = cleanValue;
                                        console.log('💰 Сумма 1 год:', cleanValue);
                                    } else if (headerText.includes('2 год')) {
                                        financialData.sum2Year = cleanValue;
                                        console.log('💰 Сумма 2 год:', cleanValue);
                                    } else if (headerText.includes('3 год')) {
                                        financialData.sum3Year = cleanValue;
                                        console.log('💰 Сумма 3 год:', cleanValue);
                                    }
                                }
                            }
                            
                            if (headerText.includes('Цена за ед')) {
                                financialData.unitPrice = cellValue;
                                console.log('💰 Цена за единицу:', cellValue);
                            }
                        });
                        
                        break;
                    }
                }
            }
        }
        
        // Поиск всех финансовых полей в таблицах (общий поиск)
        const tables = document.querySelectorAll('table');
        for (let table of tables) {
            const rows = table.querySelectorAll('tr');
            for (let row of rows) {
                const cells = row.querySelectorAll('td, th');
                if (cells.length >= 2) {
                    const fieldName = cells[0].textContent.trim();
                    const fieldValue = cells[1].textContent.trim();
                    
                    console.log(`Проверяем финансовое поле: "${fieldName}" = "${fieldValue}"`);
                    
                    // Запланированная сумма (высший приоритет)
                    if (fieldName.includes('Запланированная сумма')) {
                        const numMatch = fieldValue.match(/(\d+(?:\s*\d+)*(?:[.,]\d+)?)/);
                        if (numMatch) {
                            financialData.plannedSum = numMatch[1].replace(/\s/g, '');
                            console.log('✅ Запланированная сумма:', financialData.plannedSum);
                        }
                    }
                    
                    // Суммы по годам
                    if (fieldName.match(/Сумма \d+ год/)) {
                        const numMatch = fieldValue.match(/(\d+(?:\s*\d+)*(?:[.,]\d+)?)/);
                        if (numMatch) {
                            const year = fieldName.match(/(\d+)/)[1];
                            financialData[`sum_year_${year}`] = numMatch[1].replace(/\s/g, '');
                            console.log(`✅ Сумма ${year} год:`, financialData[`sum_year_${year}`]);
                        }
                    }
                    
                    // Цена за единицу (отдельно от общей суммы)
                    if (fieldName.includes('Цена за единицу') || fieldName.includes('Единица измерения')) {
                        const numMatch = fieldValue.match(/(\d+(?:\s*\d+)*(?:[.,]\d+)?)/);
                        if (numMatch) {
                            financialData.unitPrice = numMatch[1].replace(/\s/g, '');
                            console.log('✅ Цена за единицу:', financialData.unitPrice);
                        }
                    }
                    
                    // Общие суммы (но не цена за единицу)
                    if ((fieldName.includes('Сумма') && !fieldName.includes('за единицу')) ||
                        fieldName.includes('Общая сумма') ||
                        fieldName.includes('Итоговая сумма') ||
                        fieldName.includes('Объявленная сумма')) {
                        const numMatch = fieldValue.match(/(\d+(?:\s*\d+)*(?:[.,]\d+)?)/);
                        if (numMatch) {
                            const sum = numMatch[1].replace(/\s/g, '');
                            if (!financialData.totalSum || parseInt(sum) > parseInt(financialData.totalSum)) {
                                financialData.totalSum = sum;
                                console.log(`✅ Общая сумма из "${fieldName}":`, sum);
                            }
                        }
                    }
                    
                    // Размер авансового платежа
                    if (fieldName.includes('Размер авансового платежа')) {
                        const numMatch = fieldValue.match(/(\d+(?:\s*\d+)*(?:[.,]\d+)?)/);
                        if (numMatch) {
                            financialData.advancePayment = numMatch[1].replace(/\s/g, '');
                            console.log('✅ Размер авансового платежа:', financialData.advancePayment);
                        }
                    }
                }
            }
        }
        
        // Определяем основную сумму в приоритетном порядке
        if (financialData.plannedSum) {
            this.data.announcedSum = financialData.plannedSum;
            console.log('🎯 Основная сумма: Запланированная сумма =', financialData.plannedSum);
        } else if (financialData.sum1Year || financialData.sum2Year || financialData.sum3Year) {
            // Складываем суммы по годам
            let totalYears = parseFloat(financialData.sum1Year || 0);
            if (financialData.sum2Year) totalYears += parseFloat(financialData.sum2Year);
            if (financialData.sum3Year) totalYears += parseFloat(financialData.sum3Year);
            
            this.data.announcedSum = totalYears.toString();
            console.log('🎯 Основная сумма: Сумма по годам =', totalYears);
        } else if (financialData.totalSum) {
            this.data.announcedSum = financialData.totalSum;
            console.log('🎯 Основная сумма: Общая сумма =', financialData.totalSum);
        } else {
            // Если есть суммы по годам с старым форматом, складываем их
            let yearlyTotal = 0;
            let hasYearlySums = false;
            
            Object.keys(financialData).forEach(key => {
                if (key.startsWith('sum_year_')) {
                    yearlyTotal += parseInt(financialData[key]);
                    hasYearlySums = true;
                }
            });
            
            if (hasYearlySums && yearlyTotal > 0) {
                this.data.announcedSum = yearlyTotal.toString();
                console.log('🎯 Основная сумма: Сумма по годам (старый формат) =', yearlyTotal);
            }
        }
        
        // Сохраняем дополнительные финансовые данные
        this.data.financialDetails = financialData;
        console.log('💰 Все финансовые данные:', financialData);
    }

    // Отправка данных в popup
    sendDataToPopup() {
        console.log('📤 Отправляем данные в popup:', JSON.stringify(this.data, null, 2));
        
        chrome.runtime.sendMessage({
            action: 'updateData',
            data: this.data
        });

        // Также сохраняем в storage
        chrome.storage.local.set({ parsedData: this.data });
        console.log('💾 Данные сохранены в chrome.storage.local');
    }

    // Отправка данных на webhook
    async sendDataToWebhook() {
        const webhookUrl = 'https://beget.prodvig.kz/webhook/dd8372ba-2b7a-49e7-b677-2c484cb5cf0c';
        
        // Проверяем, что есть данные для отправки
        if (!this.data || Object.keys(this.data).length === 0) {
            console.log('❌ Нет данных для отправки на webhook');
            return;
        }

        // Детальное логирование отправляемых данных
        console.log('📤 Подготавливаем данные для отправки на webhook');
        console.log('🎯 Основные данные:');
        console.log('  - Номер лота:', this.data.lotNumber || 'не указан');
        console.log('  - Заказчик:', this.data.customerName || 'не указан');
        console.log('  - Товар:', this.data.productName || 'не указан');
        console.log('  - Количество:', this.data.quantity || 'не указано');
        console.log('  - Объявленная сумма:', this.data.announcedSum || 'не указана');
        console.log('  - Дата начала:', this.data.startDate || 'не указана');
        console.log('  - Дата окончания:', this.data.endDate || 'не указана');
        console.log('  - Источник:', this.data.source || 'не указан');
        console.log('  - URL:', this.data.sourceUrl || 'не указан');
        
        if (this.selectedLotId) {
            console.log('  - Выбранный лот ID:', this.selectedLotId);
        }

        try {
            console.log('📤 Отправляем данные на webhook:', this.data);
            
            const response = await fetch(webhookUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    timestamp: new Date().toISOString(),
                    source: 'Goszakup Parser Extension',
                    selectedLotId: this.selectedLotId || null,
                    data: this.data
                })
            });

            if (response.ok) {
                console.log('✅ Данные успешно отправлены на webhook');
                
                // Показываем уведомление об успехе
                this.showNotification('Данные отправлены на сервер!', 'success');
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        } catch (error) {
            console.error('❌ Ошибка отправки данных на webhook:', error);
            
            // Показываем уведомление об ошибке
            this.showNotification('Ошибка отправки данных на сервер', 'error');
        }
    }

    // Показ уведомлений пользователю
    showNotification(message, type = 'info') {
        // Удаляем предыдущие уведомления
        const existingNotification = document.getElementById('goszakup-notification');
        if (existingNotification) {
            existingNotification.remove();
        }

        // Создаем новое уведомление
        const notification = document.createElement('div');
        notification.id = 'goszakup-notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10001;
            padding: 12px 20px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            color: white;
            background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            animation: slideIn 0.3s ease-out;
        `;

        // Добавляем CSS анимацию
        if (!document.getElementById('goszakup-notification-styles')) {
            const style = document.createElement('style');
            style.id = 'goszakup-notification-styles';
            style.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(notification);

        // Автоматически убираем уведомление через 3 секунды
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    }
    
    // Извлечение дат из контейнера лота
    extractDatesFromContainer(container) {
        const result = { start: null, end: null };
        
        // Ищем input поля с датами в контейнере
        const inputs = container.querySelectorAll('input[type="text"]');
        const validDates = [];
        
        inputs.forEach(input => {
            const value = input.value.trim();
            if (this.isValidDateTime(value)) {
                const context = this.getInputContext(input);
                if (this.isApplicationRelatedDate(context)) {
                    validDates.push(value);
                }
            }
        });
        
        if (validDates.length >= 2) {
            validDates.sort();
            result.start = validDates[0];
            result.end = validDates[1];
        }
        
        return result;
    }
    
    // Получение контекста input поля
    getInputContext(input) {
        const contexts = [];
        
        // Проверяем label
        const label = input.closest('label') || 
                     document.querySelector(`label[for="${input.id}"]`) ||
                     input.previousElementSibling;
        if (label) contexts.push(label.textContent);
        
        // Проверяем родительские элементы
        let parent = input.parentElement;
        for (let i = 0; i < 3 && parent; i++) {
            contexts.push(parent.textContent);
            parent = parent.parentElement;
        }
        
        // Проверяем соседние элементы
        if (input.previousElementSibling) {
            contexts.push(input.previousElementSibling.textContent);
        }
        
        return contexts.join(' ').toLowerCase();
    }
    
    // Проверка, относится ли дата к приему заявок
    isApplicationRelatedDate(context) {
        const applicationKeywords = [
            'прием заявок',
            'подача заявок',
            'срок начала приема',
            'срок окончания приема',
            'начала приема заявок',
            'окончания приема заявок',
            'дата и время начала приема',
            'дата и время окончания приема',
            'начал',
            'оконч'
        ];
        
        const publicationKeywords = [
            'публикац',
            'размещен',
            'объявлен',
            'опубликован',
            'дата публикации',
            'дата размещения',
            'дата объявления'
        ];
        
        console.log('🔍 Проверяем контекст даты:', context);
        
        // Исключаем даты публикации (СТРОГО)
        for (let keyword of publicationKeywords) {
            if (context.toLowerCase().includes(keyword.toLowerCase())) {
                console.log('❌ Дата исключена по ключевому слову публикации:', keyword);
                return false;
            }
        }
        
        // Ищем ключевые слова приема заявок (СТРОГО)
        for (let keyword of applicationKeywords) {
            if (context.toLowerCase().includes(keyword.toLowerCase())) {
                console.log('✅ Дата принята по ключевому слову приема заявок:', keyword);
                return true;
            }
        }
        
        console.log('⚠️ Контекст не содержит явных указаний на прием заявок, дата отклонена');
        return false; // ИЗМЕНЕНО: по умолчанию отклоняем неопознанные даты
    }
    
    // Поиск дат в таблицах с исключениями
    findDatesInTables(selectedLotId, excludeFields) {
        const result = { start: null, end: null };
        
        let searchScope = selectedLotId ? 
            (document.querySelector(`[data-lot-id="${selectedLotId}"]`) || document) : 
            document;
            
        const tables = searchScope.querySelectorAll('table');
        
        for (let table of tables) {
            const rows = table.querySelectorAll('tr');
            
            for (let row of rows) {
                const cells = row.querySelectorAll('td, th');
                if (cells.length >= 2) {
                    const fieldName = cells[0].textContent.trim().toLowerCase();
                    const fieldValue = cells[1].textContent.trim();
                    
                    // Исключаем нежелательные поля
                    let isExcluded = false;
                    for (let exclude of excludeFields) {
                        if (fieldName.includes(exclude.toLowerCase())) {
                            isExcluded = true;
                            break;
                        }
                    }
                    
                    if (isExcluded) continue;
                    
                    // Ищем даты начала и окончания
                    if (this.isValidDateTime(fieldValue)) {
                        if ((fieldName.includes('начал') || fieldName.includes('с')) && !result.start) {
                            result.start = fieldValue;
                        } else if ((fieldName.includes('оконч') || fieldName.includes('до')) && !result.end) {
                            result.end = fieldValue;
                        }
                    }
                }
            }
        }
        
        return result;
    }
    
    // Поиск альтернативных дат для лота
    findAlternateDatesForLot(selectedLotId) {
        const result = { start: null, end: null };
        
        // Ищем все уникальные даты на странице
        const allElements = document.querySelectorAll('*');
        const foundDates = new Set();
        
        allElements.forEach(element => {
            const text = element.textContent || element.value || '';
            const dateMatch = text.match(/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/);
            if (dateMatch) {
                const context = element.textContent.toLowerCase();
                // Исключаем даты публикации
                if (!context.includes('публикац') && !context.includes('размещен')) {
                    foundDates.add(dateMatch[0]);
                }
            }
        });
        
        const sortedDates = Array.from(foundDates).sort();
        if (sortedDates.length >= 2) {
            result.start = sortedDates[0];
            result.end = sortedDates[sortedDates.length - 1];
        }
        
        return result;
    }
    
    // Проверка валидности даты-времени
    isValidDateTime(dateStr) {
        if (!dateStr || typeof dateStr !== 'string') return false;
        
        // Проверяем формат YYYY-MM-DD HH:MM:SS
        const match = dateStr.match(/^(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})$/);
        if (!match) return false;
        
        const [, year, month, day, hour, minute, second] = match;
        const date = new Date(year, month - 1, day, hour, minute, second);
        
        return date.getFullYear() == year &&
               date.getMonth() == month - 1 &&
               date.getDate() == day &&
               date.getHours() == hour &&
               date.getMinutes() == minute &&
               date.getSeconds() == second;
    }
    
    // Поиск значения по метке с поддержкой лота
    findValueByLabel(label, selectedLotId = null) {
        let searchScope = selectedLotId ? 
            (document.querySelector(`[data-lot-id="${selectedLotId}"]`) || document) : 
            document;
            
        // Поиск в таблицах
        const tables = searchScope.querySelectorAll('table');
        for (let table of tables) {
            const rows = table.querySelectorAll('tr');
            for (let row of rows) {
                const cells = row.querySelectorAll('td, th');
                if (cells.length >= 2) {
                    const cellText = cells[0].textContent.trim();
                    if (cellText.includes(label)) {
                        return cells[1].textContent.trim();
                    }
                }
            }
        }
        
        // Поиск в других элементах
        const elements = searchScope.querySelectorAll('div, span, p');
        for (let element of elements) {
            if (element.textContent.includes(label)) {
                const nextElement = element.nextElementSibling;
                if (nextElement) {
                    return nextElement.textContent.trim();
                }
            }
        }
        
        return null;
    }
    
    // Парсинг даты из строки
    parseDate(dateStr) {
        if (!dateStr) return null;
        
        // Уже в нужном формате
        if (this.isValidDateTime(dateStr)) {
            return dateStr;
        }
        
        // Попытка парсинга других форматов
        const cleanStr = dateStr.trim();
        
        // Формат DD.MM.YYYY HH:MM:SS
        let match = cleanStr.match(/(\d{1,2})\.(\d{1,2})\.(\d{4})\s+(\d{1,2}):(\d{2}):(\d{2})/);
        if (match) {
            const [, day, month, year, hour, minute, second] = match;
            return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')} ${hour.padStart(2, '0')}:${minute}:${second}`;
        }
        
        // Формат DD.MM.YYYY
        match = cleanStr.match(/(\d{1,2})\.(\d{1,2})\.(\d{4})/);
        if (match) {
            const [, day, month, year] = match;
            return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')} 00:00:00`;
        }
        
        return null;
    }
}

// Инициализация парсера
let parser;
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        parser = new GoszakupParser();
    });
} else {
    parser = new GoszakupParser();
}

// Слушаем сообщения от popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'getData') {
        sendResponse({ data: parser.data });
    } else if (request.action === 'getSelectedLotId') {
        sendResponse({ selectedLotId: parser.selectedLotId });
    } else if (request.action === 'parseData') {
        console.log('📥 Получен запрос на парсинг данных из popup');
        
        // Очищаем данные
        parser.data = {
            source: 'goszakup',
            sourceUrl: window.location.href
        };
        
        // Если есть выбранный лот, парсим для него
        if (parser.selectedLotId) {
            console.log('🎯 Парсим данные для выбранного лота:', parser.selectedLotId);
            parser.parseLotDetails(parser.selectedLotId);
            parser.parseDates(parser.selectedLotId);
            parser.parseGoszakupFinancialFields(parser.selectedLotId);
        } else {
            console.log('🔍 Парсим общие данные страницы');
            parser.parseData();
        }
        
        sendResponse({ success: true });
    }
});
